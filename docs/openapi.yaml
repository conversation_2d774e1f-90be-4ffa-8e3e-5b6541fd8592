openapi: 3.0.3
info:
  title: StalkAPI
  description: |
    Professional KOL (Key Opinion Leader) trading data API with real-time WebSocket streaming and historical data access.

    ## Features

    - **Real-time KOL Trading Stream**: Live WebSocket feed of KOL trading activity
    - **Historical Data API**: REST endpoint for the last 100 KOL transactions
    - **Credit-based Usage**: Fair resource allocation with tier-based access
    - **Multi-tier Access Control**: Flexible pricing for different use cases

    ## Authentication

    StalkAPI supports two authentication methods:
    - **JWT Token**: Include in Authorization header as `Bearer <token>`
    - **API Key**: Include in X-API-Key header

    ## Credit System

    | Endpoint/Stream | Credits | Description |
    |-----------------|---------|-------------|
    | KOL Feed History API | 3 | Historical trading data |
    | KOL Feed WebSocket | 2 per message | Real-time trading stream |

    ## Access Tiers

    | Tier | Credits/Month | Price | KOL Feed Access |
    |------|---------------|-------|-----------------|
    | Free | 1,000 | $0.00 | Disabled |
    | Basic | 10,000 | $9.99 | ✅ Full Access |
    | Premium | 100,000 | $49.99 | ✅ Full Access |
    | Enterprise | Unlimited | $199.99 | ✅ Full Access |
    
  version: 1.0.0
  contact:
    name: StalkAPI Support
    url: https://data.stalkapi.com
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: https://data.stalkapi.com
    description: Production server
  - url: http://localhost:3001
    description: Development server

security:
  - BearerAuth: []
  - ApiKeyAuth: []

paths:
  /health:
    get:
      summary: Health Check
      description: Check API server health status
      tags:
        - System
      security: []
      responses:
        '200':
          description: Server is healthy
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: healthy
                  timestamp:
                    type: string
                    format: date-time

  /auth/login:
    post:
      summary: User Login
      description: Authenticate user and receive JWT token
      tags:
        - Authentication
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - password
              properties:
                email:
                  type: string
                  format: email
                  example: <EMAIL>
                password:
                  type: string
                  example: demo123
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoginResponse'
        '401':
          description: Invalid credentials
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /auth/profile:
    get:
      summary: Get User Profile
      description: Get user information including credit balance
      tags:
        - Authentication
      responses:
        '200':
          description: User profile retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfile'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'



  /api/v1/kol-feed/history:
    get:
      summary: KOL Feed History
      description: Get historical KOL trading transactions (3 credits, Basic+ tier)
      tags:
        - KOL Feed
      parameters:
        - name: limit
          in: query
          description: Number of transactions to return (max 100)
          schema:
            type: integer
            default: 100
            minimum: 1
            maximum: 100
        - name: offset
          in: query
          description: Number of transactions to skip
          schema:
            type: integer
            default: 0
            minimum: 0
      responses:
        '200':
          description: KOL feed history retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/KolFeedHistoryResponse'
        '402':
          description: Insufficient credits
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreditError'
        '403':
          description: Access denied - insufficient tier
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
    ApiKeyAuth:
      type: apiKey
      in: header
      name: X-API-Key

  schemas:
    LoginResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        token:
          type: string
          example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
        user:
          type: object
          properties:
            id:
              type: string
              format: uuid
            email:
              type: string
              format: email
            tier_name:
              type: string
              example: basic
            credits_remaining:
              type: integer
              example: 9950

    UserProfile:
      type: object
      properties:
        user:
          type: object
          properties:
            id:
              type: string
              format: uuid
            email:
              type: string
              format: email
            tier_name:
              type: string
              example: basic
            credits_remaining:
              type: integer
              example: 9950
            credits_used_this_month:
              type: integer
              example: 50
            max_credits_per_month:
              type: integer
              example: 10000



    KolFeedHistoryResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: array
          items:
            $ref: '#/components/schemas/KolTransaction'
        pagination:
          type: object
          properties:
            limit:
              type: integer
            offset:
              type: integer
            total:
              type: integer
            returned:
              type: integer
        credits_consumed:
          type: integer
          example: 3
        message:
          type: string
          example: KOL feed history retrieved successfully

    KolTransaction:
      type: object
      properties:
        timestamp:
          type: integer
          format: int64
          example: 1748919450434
        kol_label:
          type: string
          example: TraderSZ
        wallet:
          type: string
          example: private
        kol_avatar:
          type: string
          format: uri
          nullable: true
        tokenIn:
          $ref: '#/components/schemas/Token'
        tokenOut:
          $ref: '#/components/schemas/Token'
        signature:
          type: string
          example: private
        transactionType:
          type: string
          enum: [buy, sell]
        chain:
          type: string
          example: solana
        socials:
          type: array
          items:
            type: object

    Token:
      type: object
      properties:
        symbol:
          type: string
          example: SOL
        name:
          type: string
          example: Wrapped SOL
        logo:
          type: string
          format: uri
        tokenAmountString:
          type: string
          example: "13.0"
        amount:
          type: number
          example: 13.0
        tokenInAmountUsd:
          type: number
          example: 2095.16
        price:
          type: number
          example: 161.17
        mint:
          type: string
          example: So11111111111111111111111111111111111111112

    Error:
      type: object
      properties:
        error:
          type: string
        code:
          type: string
        timestamp:
          type: string
          format: date-time

    CreditError:
      type: object
      properties:
        error:
          type: string
          example: Insufficient credits
        code:
          type: string
          example: INSUFFICIENT_CREDITS
        credits_remaining:
          type: integer
          example: 0
        credits_required:
          type: integer
          example: 3

tags:
  - name: System
    description: System health and status endpoints
  - name: Authentication
    description: User authentication and profile management
  - name: KOL Feed
    description: KOL trading activity data endpoints and WebSocket streams

{"info": {"_postman_id": "stalkapi-clean-collection", "name": "StalkAPI - KOL Feed API", "description": "Professional KOL (Key Opinion Leader) trading data API with real-time WebSocket streaming and historical data access.\n\n## Features\n- Real-time KOL trading stream via WebSocket\n- Historical KOL trading data via REST API\n- Credit-based usage tracking\n- Multi-tier access control\n\n## Authentication\n- JWT Token: Include in Authorization header as `Bearer <token>`\n- API Key: Include in X-API-Key header\n\n## Base URL\n- Development: `http://localhost:3001`\n- Production: `https://your-domain.com`", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "🏥 System Health", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}, "description": "Check API health status. No authentication required."}, "response": []}], "description": "System health and status endpoints."}, {"name": "🔐 Authentication", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"{{demo_email}}\",\n  \"password\": \"{{demo_password}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}, "description": "Authenticate user and get JWT token and API key."}, "response": []}, {"name": "Get Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/auth/profile", "host": ["{{base_url}}"], "path": ["auth", "profile"]}, "description": "Get user profile and credit information."}, "response": []}], "description": "User authentication and profile management."}, {"name": "📊 KOL Feed API", "item": [{"name": "KOL Feed History (API Key)", "request": {"method": "GET", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}], "url": {"raw": "{{base_url}}/api/v1/kol-feed/history?limit=10&offset=0", "host": ["{{base_url}}"], "path": ["api", "v1", "kol-feed", "history"], "query": [{"key": "limit", "value": "10", "description": "Number of transactions to return (max 100)"}, {"key": "offset", "value": "0", "description": "Number of transactions to skip"}]}, "description": "Get historical KOL trading data using API Key authentication. Costs 3 credits per request."}, "response": []}, {"name": "KOL Feed History (JWT)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/kol-feed/history?limit=10&offset=0", "host": ["{{base_url}}"], "path": ["api", "v1", "kol-feed", "history"], "query": [{"key": "limit", "value": "10", "description": "Number of transactions to return (max 100)"}, {"key": "offset", "value": "0", "description": "Number of transactions to skip"}]}, "description": "Get historical KOL trading data using JWT authentication. Costs 3 credits per request."}, "response": []}], "description": "KOL trading activity data endpoints."}, {"name": "🔌 WebSocket API", "item": [{"name": "WebSocket Info", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/ws-api/info", "host": ["{{base_url}}"], "path": ["ws-api", "info"]}, "description": "Get WebSocket server connection details and status."}, "response": []}, {"name": "Available Streams", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/ws-api/streams", "host": ["{{base_url}}"], "path": ["ws-api", "streams"]}, "description": "Get list of available WebSocket streams for user's tier."}, "response": []}, {"name": "Stream Credits Info", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/ws-api/credits?stream=kol-feed", "host": ["{{base_url}}"], "path": ["ws-api", "credits"], "query": [{"key": "stream", "value": "kol-feed", "description": "Specific stream to get credit info for"}]}, "description": "Get credit costs and information for WebSocket streams."}, "response": []}], "description": "WebSocket server information and stream management endpoints."}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Auto-set variables from login response", "if (pm.response && pm.response.json()) {", "    const response = pm.response.json();", "    if (response.token) {", "        pm.environment.set('jwt_token', response.token);", "    }", "    if (response.user && response.user.api_key) {", "        pm.environment.set('api_key', response.user.api_key);", "    }", "}"]}}], "variable": [{"key": "base_url", "value": "http://localhost:3001", "type": "string"}, {"key": "demo_email", "value": "<EMAIL>", "type": "string"}, {"key": "demo_password", "value": "demo123", "type": "string"}]}
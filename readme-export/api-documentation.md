# Robust API Engine Documentation

## Overview

The Robust API Engine is a comprehensive, credit-based API system with WebSocket support, tier-based access control, and real-time streaming capabilities. It provides a scalable foundation for building modern APIs with advanced features like rate limiting, caching, and usage tracking.

## Features

- **Credit-based Usage Tracking**: Monitor and control API usage with a flexible credit system
- **Tier-based Access Control**: Multiple access tiers with different permissions and limits
- **RESTful API Endpoints**: GET and POST endpoints with comprehensive functionality
- **WebSocket Server**: Real-time communication with subscribe/unsubscribe capabilities
- **Redis Pub/Sub Integration**: Distributed event handling and caching
- **Rate Limiting**: Intelligent rate limiting based on user tiers
- **Authentication**: JWT tokens and API key authentication
- **Comprehensive Logging**: Detailed usage analytics and monitoring
- **Caching**: Redis-based caching for improved performance

## Quick Start

### 1. Environment Setup

Copy `.env.example` to `.env` and configure your settings:

```bash
cp .env.example .env
```

### 2. Database Setup

Run the migration script to set up your PostgreSQL database:

```bash
npm run db:migrate
```

### 3. Start the Server

```bash
# Development
npm run dev

# Production
npm start
```

## Authentication

### JWT Token Authentication

Include the JWT token in the Authorization header:

```
Authorization: Bearer YOUR_JWT_TOKEN
```

### API Key Authentication

Include the API key in the X-API-Key header:

```
X-API-Key: YOUR_API_KEY
```

## Access Tiers

| Tier | Credits/Month | Requests/Min | WebSocket Connections | Price | Status |
|------|---------------|--------------|----------------------|-------|--------|
| Free | 1,000 | 10 | 1 | $0.00 | Disabled* |
| Basic | 10,000 | 60 | 3 | $9.99 | Enabled |
| Premium | 100,000 | 300 | 10 | $49.99 | Enabled |
| Enterprise | Unlimited | 1,000 | 50 | $199.99 | Enabled |

*The free tier is currently disabled but can be enabled through admin controls.

## API Endpoints

### Authentication Endpoints

#### Register User
```http
POST /auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securepassword",
  "tier_id": 1
}
```

#### Login
```http
POST /auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securepassword"
}
```

#### Get Profile
```http
GET /auth/profile
Authorization: Bearer YOUR_JWT_TOKEN
```

#### Refresh Token
```http
POST /auth/refresh
Content-Type: application/json

{
  "refreshToken": "YOUR_REFRESH_TOKEN"
}
```

### API Endpoints

#### Status Check (Public)
```http
GET /api/v1/status
```

#### Demo Endpoint (All Tiers)
```http
GET /api/v1/demo
Authorization: Bearer YOUR_JWT_TOKEN
```
**Credits:** 1 per request

#### Data Endpoint (Basic+)
```http
GET /api/v1/data?limit=10&offset=0
Authorization: Bearer YOUR_JWT_TOKEN
```
**Credits:** 2 per request

#### Analytics Endpoint (Premium+)
```http
GET /api/v1/analytics?timeframe=24h
Authorization: Bearer YOUR_JWT_TOKEN
```
**Credits:** 5 per request

#### Submit Data
```http
POST /api/v1/submit
Authorization: Bearer YOUR_JWT_TOKEN
Content-Type: application/json

{
  "data": {
    "key": "value"
  },
  "metadata": {
    "source": "client"
  }
}
```
**Credits:** 3 per request

#### Batch Processing (Enterprise)
```http
POST /api/v1/batch
Authorization: Bearer YOUR_JWT_TOKEN
Content-Type: application/json

{
  "items": [
    {"id": 1, "data": "value1"},
    {"id": 2, "data": "value2"}
  ]
}
```
**Credits:** 10 per request

#### Search
```http
GET /api/v1/search?q=query&limit=10&category=tech
Authorization: Bearer YOUR_JWT_TOKEN
```
**Credits:** 2 per request

#### KOL Feed History (Basic+)
```http
GET /api/v1/kol-feed/history?limit=50&offset=0
Authorization: Bearer YOUR_JWT_TOKEN
```
**Credits:** 3 per request

Returns the last 100 KOL trading transactions with pagination support.

### Admin Endpoints (Admin API Key Required)

**Authentication:** All admin endpoints require an admin API key in the `X-Admin-API-Key` header.

#### Get All Tiers
```http
GET /admin/tiers
X-Admin-API-Key: YOUR_ADMIN_API_KEY
```

#### Enable Tier
```http
POST /admin/tiers/{tierId}/enable
X-Admin-API-Key: YOUR_ADMIN_API_KEY
```

#### Disable Tier
```http
POST /admin/tiers/{tierId}/disable
X-Admin-API-Key: YOUR_ADMIN_API_KEY
```

#### Update Tier Configuration
```http
PUT /admin/tiers/{tierId}
X-Admin-API-Key: YOUR_ADMIN_API_KEY
Content-Type: application/json

{
  "name": "updated_tier_name",
  "description": "Updated description",
  "max_credits_per_month": 5000,
  "max_requests_per_minute": 30,
  "max_websocket_connections": 2,
  "allowed_endpoints": ["/api/v1/demo", "/api/v1/data"],
  "allowed_streams": ["demo-stream", "data-stream"],
  "price_per_month": 19.99
}
```

#### Get Tier Statistics
```http
GET /admin/tiers/{tierId}/stats
X-Admin-API-Key: YOUR_ADMIN_API_KEY
```

#### Get Admin Info
```http
GET /admin/me
X-Admin-API-Key: YOUR_ADMIN_API_KEY
```

#### Admin User Management (System Admin Only)
```http
# Get all admin users
GET /admin/admins
X-Admin-API-Key: YOUR_ADMIN_API_KEY

# Create new admin user
POST /admin/admins
X-Admin-API-Key: YOUR_ADMIN_API_KEY
Content-Type: application/json

{
  "name": "Admin Name",
  "email": "<EMAIL>",
  "permissions": ["tiers:read", "tiers:write"]
}

# Update admin user
PUT /admin/admins/{adminId}
X-Admin-API-Key: YOUR_ADMIN_API_KEY
Content-Type: application/json

{
  "name": "Updated Name",
  "permissions": ["tiers:read", "tiers:write", "users:read"]
}
```

## Credit System

The API uses a credit-based usage tracking system where each API call consumes a specific number of credits based on the endpoint's complexity and resource requirements.

### How Credits Work

**Credit Consumption:**
- Each API endpoint has a predefined credit cost
- Credits are consumed **before** the API call is processed
- If insufficient credits, the request is rejected with a 402 error
- Credits are deducted atomically using PostgreSQL functions

**Credit Allocation:**
- Credits are allocated based on the user's access tier
- Monthly credit limits are enforced per tier
- Unlimited tiers use -1 to indicate no limit

### Credit Costs by Endpoint

| Endpoint | Credit Cost | Description |
|----------|-------------|-------------|
| `GET /api/v1/demo` | 1 | Basic demo endpoint |
| `GET /api/v1/data` | 2 | Data retrieval endpoint |
| `GET /api/v1/analytics` | 5 | Analytics processing |
| `GET /api/v1/search` | 2 | Search functionality |
| `POST /api/v1/submit` | 3 | Data submission endpoint |
| `GET /api/v1/kol-feed/history` | 3 | KOL trading history |
| `POST /api/v1/batch` | 10 | Batch processing |
| WebSocket connections | 0 | No credit cost for connections |
| **WebSocket stream messages** | **Variable** | **Credit cost per message received** |

### WebSocket Stream Credit Costs

| Stream | Credits per Message | Description |
|--------|-------------------|-------------|
| `demo-stream` | 1 | Demo data for testing |
| `data-stream` | 2 | Real-time data feed |
| `kol-feed` | **2** | **Real-time KOL trading activity** |
| `analytics-stream` | 5 | Analytics metrics |
| `enterprise-stream` | 1 | High-frequency enterprise data |

**Note:** WebSocket stream credits are deducted **per message received**, not per connection. Users with insufficient credits will receive credit warning messages instead of stream data.

### Credit Management

#### Setting Credit Costs

Credit costs are defined **directly in each route definition** using middleware functions in `src/routes/api.js`:

```javascript
// Example route with credit cost
router.get('/demo',
    verifyToken,
    checkEndpointAccess('/api/v1/demo'),
    checkCredits(1),        // Check user has 1 credit before processing
    consumeCredits(1),      // Consume 1 credit after successful response
    async (req, res) => {
        // Route handler logic...
    }
);

// To change credit costs, modify the numbers in the middleware:
checkCredits(2),        // Changed from 1 to 2
consumeCredits(2),      // Changed from 1 to 2
```

#### Database Functions

The system uses PostgreSQL functions for atomic credit operations:

```sql
-- Consume credits atomically
SELECT consume_credits(
    user_id,           -- User UUID
    credits_to_consume, -- Number of credits
    endpoint,          -- API endpoint
    method,            -- HTTP method
    ip_address,        -- User IP
    user_agent,        -- User agent
    request_id,        -- Unique request ID
    tier_id,           -- User's tier ID
    max_credits,       -- Monthly credit limit
    current_month      -- Current month for tracking
);
```

#### Tier-Based Credit Limits

| Tier | Monthly Credits | Behavior |
|------|----------------|----------|
| Free | 1,000 | Hard limit, requests blocked when exceeded |
| Basic | 10,000 | Hard limit, requests blocked when exceeded |
| Premium | 100,000 | Hard limit, requests blocked when exceeded |
| Enterprise | Unlimited (-1) | No credit consumption tracking |

### Credit Tracking

#### User Credit Information

Users can check their credit status via the profile endpoint:

```http
GET /auth/profile
Authorization: Bearer YOUR_JWT_TOKEN
```

**Response:**
```json
{
  "user": {
    "credits_remaining": 9950,
    "credits_used_this_month": 50,
    "total_credits_purchased": 0,
    "max_credits_per_month": 10000,
    "tier_name": "basic"
  },
  "usage_summary": {
    "current_month": "2024-01",
    "credits_used": 50,
    "credits_remaining": 9950,
    "percentage_used": 0.5
  }
}
```

#### Credit Consumption Logging

All credit consumption is logged in the `api_usage_logs` table:

```sql
-- View recent credit usage
SELECT
    endpoint,
    method,
    credits_consumed,
    ip_address,
    created_at
FROM api_usage_logs
WHERE user_id = 'your-user-id'
ORDER BY created_at DESC
LIMIT 10;
```

### Credit Responses

#### Successful Request
```json
{
  "success": true,
  "data": { ... },
  "credits_remaining": 9949,
  "credits_used": 1
}
```

#### Insufficient Credits
```json
{
  "error": "Insufficient credits",
  "code": "INSUFFICIENT_CREDITS",
  "credits_remaining": 0,
  "credits_required": 5,
  "message": "You need 5 credits but only have 0 remaining"
}
```

#### Credit Limit Exceeded
```json
{
  "error": "Monthly credit limit exceeded",
  "code": "CREDIT_LIMIT_EXCEEDED",
  "credits_used_this_month": 10000,
  "max_credits_per_month": 10000,
  "message": "You have used all 10000 credits for this month"
}
```

### Admin Credit Management

Admins can manage user credits through the admin API:

#### View User Credits
```http
GET /admin/users/{userId}/credits
X-Admin-API-Key: YOUR_ADMIN_API_KEY
```

#### Add Credits to User
```http
POST /admin/users/{userId}/credits
X-Admin-API-Key: YOUR_ADMIN_API_KEY
Content-Type: application/json

{
  "credits_to_add": 1000,
  "reason": "Promotional bonus"
}
```

#### Reset Monthly Credits
```http
POST /admin/users/{userId}/credits/reset
X-Admin-API-Key: YOUR_ADMIN_API_KEY
```

### Credit System Configuration

#### Environment Variables

```bash
# Credit system settings (optional)
CREDIT_TRACKING_ENABLED=true
CREDIT_GRACE_PERIOD=false
CREDIT_WARNING_THRESHOLD=100
```

#### Database Configuration

The credit system uses several database tables:

1. **users** - Stores current credit balances
2. **access_tiers** - Defines credit limits per tier
3. **api_usage_logs** - Tracks all credit consumption
4. **credit_transactions** - Records credit purchases/adjustments

#### Customizing Credit Costs

To modify credit costs for endpoints:

1. **Update route definitions** (`src/routes/api.js`):
```javascript
// Change from 1 to 2 credits for demo endpoint
router.get('/demo',
    verifyToken,
    checkEndpointAccess('/api/v1/demo'),
    checkCredits(2),        // Changed from 1 to 2
    consumeCredits(2),      // Changed from 1 to 2
    async (req, res) => {
        // Route handler logic...
    }
);
```

2. **Update documentation** to reflect new costs

3. **Restart the application** to apply changes

#### Monthly Credit Reset

Credits reset automatically on the 1st of each month via a database function:

```sql
-- Manual monthly reset (admin only)
SELECT reset_monthly_credits();
```

### Best Practices

1. **Monitor Usage**: Regularly check credit consumption patterns
2. **Set Alerts**: Implement alerts when users approach credit limits
3. **Tier Upgrades**: Provide clear upgrade paths for users hitting limits
4. **Cost Optimization**: Adjust credit costs based on actual resource usage
5. **Grace Period**: Consider implementing a grace period for slight overages

### Troubleshooting

#### Common Issues

1. **Credits not deducting**: Check if credit tracking is enabled
2. **Negative credits**: Database constraint prevents this
3. **Wrong credit costs**: Verify endpoint mapping in middleware
4. **Monthly reset issues**: Check database function execution

#### Debug Commands

```sql
-- Check user credit status
SELECT credits_remaining, credits_used_this_month, max_credits_per_month
FROM users u
JOIN access_tiers at ON u.tier_id = at.id
WHERE u.email = '<EMAIL>';

-- View recent credit transactions
SELECT * FROM api_usage_logs
WHERE user_id = 'user-uuid'
ORDER BY created_at DESC
LIMIT 20;
```

## WebSocket API

### Connection

Connect to the WebSocket server using either JWT token or API key:

```javascript
// Using JWT token
const ws = new WebSocket('ws://localhost:3000/ws?token=YOUR_JWT_TOKEN');

// Using API key
const ws = new WebSocket('ws://localhost:3000/ws?apiKey=YOUR_API_KEY');
```

### Message Format

All WebSocket messages use JSON format:

```json
{
  "type": "message_type",
  "payload": {
    "key": "value"
  }
}
```

### Subscribe to Stream

```json
{
  "type": "subscribe",
  "payload": {
    "stream": "demo-stream"
  }
}
```

### Unsubscribe from Stream

```json
{
  "type": "unsubscribe",
  "payload": {
    "stream": "demo-stream"
  }
}
```

### Ping/Pong

```json
{
  "type": "ping"
}
```

### Available Streams

| Stream | Description | Required Tier | Credits/Message |
|--------|-------------|---------------|-----------------|
| demo-stream | Demo data for testing | Free+ | 1 |
| data-stream | Real-time data feed | Basic+ | 2 |
| analytics-stream | Analytics metrics | Premium+ | 5 |
| enterprise-stream | High-frequency data | Enterprise | 1 |
| **kol-feed** | **Real-time KOL trading activity** | **Basic+** | **2** |

#### KOL Feed Stream

The KOL feed provides real-time trading activity from Key Opinion Leaders (KOLs) in the cryptocurrency space. This stream delivers live transaction data including:

- **KOL trader information** (name, avatar, social profiles)
- **Token swap details** (buy/sell transactions)
- **Transaction amounts** and USD values
- **Token metadata** (symbols, names, logos, contract addresses)
- **Privacy handling** (private wallets show "private" instead of addresses)

**Example KOL Feed Message:**
```json
{
  "type": "stream_data",
  "stream": "kol-feed",
  "data": {
    "timestamp": 1748919450434,
    "kol_label": "TraderSZ",
    "wallet": "private",
    "kol_avatar": "https://example.com/avatar.jpg",
    "tokenIn": {
      "symbol": "SOL",
      "name": "Wrapped SOL",
      "amount": 13.0,
      "tokenInAmountUsd": 2095.16,
      "mint": "So11111111111111111111111111111111111111112"
    },
    "tokenOut": {
      "symbol": "USDUC",
      "name": "unstable coin",
      "amount": 294116.82,
      "tokenOutAmountUsd": 2041.57,
      "mint": "CB9dDufT3ZuQXqqSfa1c5kY935TEreyBw9XJXxHKpump"
    },
    "signature": "private",
    "transactionType": "buy",
    "chain": "solana",
    "socials": [
      {
        "type": "x",
        "handle": "trader1sz",
        "followers": 658700
      }
    ]
  },
  "timestamp": 1748919450434
}

## WebSocket Management Endpoints

### Get WebSocket Info
```http
GET /ws-api/info
Authorization: Bearer YOUR_JWT_TOKEN
```

### List Available Streams
```http
GET /ws-api/streams
Authorization: Bearer YOUR_JWT_TOKEN
```

### Get Active Sessions
```http
GET /ws-api/sessions
Authorization: Bearer YOUR_JWT_TOKEN
```

### Disconnect Session
```http
DELETE /ws-api/sessions/{sessionId}
Authorization: Bearer YOUR_JWT_TOKEN
```

### WebSocket Statistics
```http
GET /ws-api/stats?days=7
Authorization: Bearer YOUR_JWT_TOKEN
```

### Stream Credit Management

#### Get Stream Credit Information
```http
GET /ws-api/credits
Authorization: Bearer YOUR_JWT_TOKEN
```

**Response:**
```json
{
  "success": true,
  "credit_info": {
    "credits_enabled": true,
    "user_credits": {
      "remaining": 9941,
      "used_this_month": 59,
      "tier": "basic"
    },
    "stream_costs": {
      "demo-stream": 1,
      "data-stream": 2,
      "kol-feed": 2,
      "analytics-stream": 5,
      "enterprise-stream": 1
    }
  }
}
```

#### Get Specific Stream Credit Info
```http
GET /ws-api/credits?stream=kol-feed
Authorization: Bearer YOUR_JWT_TOKEN
```

**Response:**
```json
{
  "success": true,
  "credit_info": {
    "credits_enabled": true,
    "user_credits": {
      "remaining": 9941,
      "used_this_month": 59,
      "tier": "basic"
    },
    "stream_cost": 2,
    "can_afford": true,
    "stream_stats": {
      "total_messages": 156,
      "total_credits_consumed": 312,
      "unique_users": 8
    }
  }
}
```

#### Get Stream Credit Usage Statistics
```http
GET /ws-api/credits/stats?days=7&stream=kol-feed
Authorization: Bearer YOUR_JWT_TOKEN
```

**Response:**
```json
{
  "success": true,
  "credit_stats": {
    "period_days": 7,
    "stream_filter": "kol-feed",
    "daily_usage": [
      {
        "date": "2024-01-15T00:00:00.000Z",
        "stream": "kol-feed",
        "credits_consumed": 24,
        "message_count": 12
      }
    ],
    "summary": {
      "total_credits": 156,
      "total_messages": 78,
      "avg_credits_per_day": 22
    }
  }
}
```

### Stream Credit Warnings

When users don't have sufficient credits for stream messages, they receive a warning instead of the stream data:

```json
{
  "type": "credit_warning",
  "stream": "kol-feed",
  "message": "Insufficient credits to receive stream data",
  "required_credits": 2,
  "timestamp": 1748919450434
}
```

## Error Handling

### HTTP Status Codes

- `200` - Success
- `400` - Bad Request
- `401` - Unauthorized
- `402` - Payment Required (Insufficient Credits)
- `403` - Forbidden (Insufficient Tier)
- `404` - Not Found
- `429` - Too Many Requests (Rate Limited)
- `500` - Internal Server Error

### Error Response Format

```json
{
  "error": "Error message",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "path": "/api/v1/endpoint",
  "method": "GET"
}
```

## Rate Limiting

Rate limits are applied based on user tiers:

- **Free**: 10 requests per 15 minutes
- **Basic**: 60 requests per 15 minutes  
- **Premium**: 300 requests per 15 minutes
- **Enterprise**: 1000 requests per 15 minutes

Rate limit headers are included in responses:

```
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 59
X-RateLimit-Reset: 1640995200
```

## Credit System

### Credit Consumption

Credits are consumed automatically when making API calls. The number of credits consumed depends on the endpoint:

- Simple endpoints: 1-2 credits
- Complex endpoints: 3-5 credits
- Batch operations: 10+ credits

### Credit Status

Check your credit status:

```http
GET /auth/profile
Authorization: Bearer YOUR_JWT_TOKEN
```

Response includes:
```json
{
  "credits": {
    "credits_remaining": 950,
    "credits_used_this_month": 50,
    "max_credits_per_month": 1000,
    "tier_name": "free",
    "unlimited": false
  }
}
```

## Caching

The API uses Redis for caching to improve performance:

- User data: 5 minutes
- Analytics data: 5 minutes  
- Search results: 2 minutes
- Stream definitions: 10 minutes

Cache headers indicate if data was served from cache:

```json
{
  "data": {...},
  "cached": true
}
```

## Health Check

Monitor API health:

```http
GET /health
```

Response:
```json
{
  "status": "healthy",
  "services": {
    "database": "healthy",
    "redis": "healthy"
  },
  "uptime": 3600,
  "memory": {...},
  "version": "1.0.0"
}
```

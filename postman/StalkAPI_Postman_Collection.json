{"info": {"_postman_id": "12345678-1234-1234-1234-123456789abc", "name": "StalkAPI - Robust API Engine", "description": "Complete API collection for StalkAPI with credit-based usage tracking, tier-based access control, and real-time WebSocket streaming.\n\n**Demo Credentials:**\n- Email: <EMAIL>\n- Password: demo123\n- API Key: demo_api_key_12345 (Default authentication method)\n- Tier: Basic (10,000 credits)\n\n**Authentication Methods:**\n- API Key (Default): X-API-Key header\n- JWT Token (Alternative): Authorization: Bearer header\n\n**Base URLs:**\n- Development: http://localhost:3001\n- Production: https://data.stalkapi.com\n\n**New Features:**\n- WebSocket Stream Credits (credits per message)\n- KOL Feed real-time trading data\n- Stream credit management endpoints\n- Admin user management", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Authentication", "item": [{"name": "Register User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.environment.set('jwt_token', response.token);", "    pm.environment.set('user_id', response.user.id);", "    pm.environment.set('api_key', response.apiKey);", "    console.log('User registered and tokens saved to environment');", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\",\n  \"tier_id\": 2\n}"}, "url": {"raw": "{{base_url}}/auth/register", "host": ["{{base_url}}"], "path": ["auth", "register"]}, "description": "Register a new user account. Returns JWT token and API key."}, "response": []}, {"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.environment.set('jwt_token', response.token);", "    pm.environment.set('user_id', response.user.id);", "    console.log('JWT Token saved to environment');", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"demo123\"\n}"}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}, "description": "Authenticate user and receive JWT token. The token is automatically saved to environment variables for use in other requests."}, "response": []}, {"name": "Get Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/auth/profile", "host": ["{{base_url}}"], "path": ["auth", "profile"]}, "description": "Get user profile information including credits, tier, and usage statistics."}, "response": []}, {"name": "Refresh <PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.environment.set('jwt_token', response.token);", "    console.log('JWT Token refreshed and saved');", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/auth/refresh", "host": ["{{base_url}}"], "path": ["auth", "refresh"]}, "description": "Refresh JWT token to extend session."}, "response": []}], "description": "Authentication endpoints for login, profile, and token refresh."}, {"name": "API Endpoints", "item": [{"name": "Root Endpoint", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/", "host": ["{{base_url}}"], "path": [""]}, "description": "Get API information and WebSocket endpoint. No authentication required."}, "response": []}, {"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}, "description": "Check API health status. No authentication required."}, "response": []}, {"name": "Demo Endpoint", "request": {"method": "GET", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}], "url": {"raw": "{{base_url}}/api/v1/demo", "host": ["{{base_url}}"], "path": ["api", "v1", "demo"]}, "description": "Demo endpoint available to all tiers. Costs 1 credit per request."}, "response": []}, {"name": "Data Endpoint", "request": {"method": "GET", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}], "url": {"raw": "{{base_url}}/api/v1/data", "host": ["{{base_url}}"], "path": ["api", "v1", "data"]}, "description": "Data endpoint for Basic+ tiers. Costs 2 credits per request."}, "response": []}, {"name": "Analytics Endpoint", "request": {"method": "GET", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}], "url": {"raw": "{{base_url}}/api/v1/analytics", "host": ["{{base_url}}"], "path": ["api", "v1", "analytics"]}, "description": "Analytics endpoint for Premium+ tiers. Costs 5 credits per request."}, "response": []}, {"name": "Search Endpoint", "request": {"method": "GET", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}], "url": {"raw": "{{base_url}}/api/v1/search?q=example&limit=10", "host": ["{{base_url}}"], "path": ["api", "v1", "search"], "query": [{"key": "q", "value": "example"}, {"key": "limit", "value": "10"}]}, "description": "Search endpoint for Premium+ tiers. Costs 3 credits per request."}, "response": []}, {"name": "Submit Data", "request": {"method": "POST", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"data\": {\n    \"type\": \"sample\",\n    \"value\": 123.45,\n    \"category\": \"test\"\n  },\n  \"metadata\": {\n    \"source\": \"postman\",\n    \"timestamp\": \"2024-01-15T10:30:00Z\"\n  }\n}"}, "url": {"raw": "{{base_url}}/api/v1/submit", "host": ["{{base_url}}"], "path": ["api", "v1", "submit"]}, "description": "Submit data for processing. Available to all tiers. Costs 3 credits per request."}, "response": []}, {"name": "Batch Processing", "request": {"method": "POST", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"items\": [\n    {\n      \"type\": \"process\",\n      \"data\": \"sample data 1\"\n    },\n    {\n      \"type\": \"analyze\",\n      \"data\": \"sample data 2\"\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/api/v1/batch", "host": ["{{base_url}}"], "path": ["api", "v1", "batch"]}, "description": "Batch processing endpoint for Enterprise tier only. Costs 10 credits per request."}, "response": []}], "description": "Main API endpoints using API key authentication."}, {"name": "API Endpoints (JWT Auth)", "item": [{"name": "Demo Endpoint (JWT)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/demo", "host": ["{{base_url}}"], "path": ["api", "v1", "demo"]}, "description": "Demo endpoint using JWT token authentication."}, "response": []}, {"name": "Data Endpoint (JWT)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/data", "host": ["{{base_url}}"], "path": ["api", "v1", "data"]}, "description": "Data endpoint using JWT token authentication."}, "response": []}], "description": "API endpoints using JWT token authentication as an alternative to API key."}, {"name": "WebSocket API", "item": [{"name": "WebSocket Info", "request": {"method": "GET", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}], "url": {"raw": "{{base_url}}/ws-api/info", "host": ["{{base_url}}"], "path": ["ws-api", "info"]}, "description": "Get WebSocket server information and connection details."}, "response": []}, {"name": "Available Streams", "request": {"method": "GET", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}], "url": {"raw": "{{base_url}}/ws-api/streams", "host": ["{{base_url}}"], "path": ["ws-api", "streams"]}, "description": "Get list of available streams for the user's tier."}, "response": []}, {"name": "WebSocket Sessions", "request": {"method": "GET", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}], "url": {"raw": "{{base_url}}/ws-api/sessions", "host": ["{{base_url}}"], "path": ["ws-api", "sessions"]}, "description": "Get current WebSocket sessions for the user."}, "response": []}, {"name": "WebSocket Statistics", "request": {"method": "GET", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}], "url": {"raw": "{{base_url}}/ws-api/stats?days=7", "host": ["{{base_url}}"], "path": ["ws-api", "stats"], "query": [{"key": "days", "value": "7"}]}, "description": "Get WebSocket usage statistics for the past 7 days."}, "response": []}, {"name": "Stream Credit Information", "request": {"method": "GET", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}], "url": {"raw": "{{base_url}}/ws-api/credits", "host": ["{{base_url}}"], "path": ["ws-api", "credits"]}, "description": "Get stream credit information including costs for all streams and user credit status."}, "response": []}, {"name": "Specific Stream Credit Info", "request": {"method": "GET", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}], "url": {"raw": "{{base_url}}/ws-api/credits?stream=kol-feed", "host": ["{{base_url}}"], "path": ["ws-api", "credits"], "query": [{"key": "stream", "value": "kol-feed"}]}, "description": "Get credit information for a specific stream including cost and statistics."}, "response": []}, {"name": "Stream Credit Usage Statistics", "request": {"method": "GET", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}], "url": {"raw": "{{base_url}}/ws-api/credits/stats?days=7&stream=kol-feed", "host": ["{{base_url}}"], "path": ["ws-api", "credits", "stats"], "query": [{"key": "days", "value": "7"}, {"key": "stream", "value": "kol-feed"}]}, "description": "Get detailed credit usage statistics for WebSocket streams."}, "response": []}, {"name": "WebSocket Test", "request": {"method": "POST", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}], "url": {"raw": "{{base_url}}/ws-api/test", "host": ["{{base_url}}"], "path": ["ws-api", "test"]}, "description": "Test WebSocket connection capabilities and get connection URLs."}, "response": []}], "description": "WebSocket API endpoints for connection info, stream management, and credit tracking."}, {"name": "Admin API (Admin Key Required)", "item": [{"name": "Get All Tiers", "request": {"method": "GET", "header": [{"key": "X-Admin-API-Key", "value": "{{admin_api_key}}"}], "url": {"raw": "{{base_url}}/admin/tiers", "host": ["{{base_url}}"], "path": ["admin", "tiers"]}, "description": "Get all access tiers including disabled ones. Requires admin API key."}, "response": []}, {"name": "Enable Tier", "request": {"method": "POST", "header": [{"key": "X-Admin-API-Key", "value": "{{admin_api_key}}"}], "url": {"raw": "{{base_url}}/admin/tiers/1/enable", "host": ["{{base_url}}"], "path": ["admin", "tiers", "1", "enable"]}, "description": "Enable a specific tier (e.g., enable free tier). Requires admin API key."}, "response": []}, {"name": "Disable Tier", "request": {"method": "POST", "header": [{"key": "X-Admin-API-Key", "value": "{{admin_api_key}}"}], "url": {"raw": "{{base_url}}/admin/tiers/1/disable", "host": ["{{base_url}}"], "path": ["admin", "tiers", "1", "disable"]}, "description": "Disable a specific tier. Cannot disable tiers with active users. Requires admin API key."}, "response": []}, {"name": "Update Tier Configuration", "request": {"method": "PUT", "header": [{"key": "X-Admin-API-Key", "value": "{{admin_api_key}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"updated_tier_name\",\n  \"description\": \"Updated tier description\",\n  \"max_credits_per_month\": 5000,\n  \"max_requests_per_minute\": 30,\n  \"max_websocket_connections\": 2,\n  \"allowed_endpoints\": [\"/api/v1/demo\", \"/api/v1/data\"],\n  \"allowed_streams\": [\"demo-stream\", \"data-stream\"],\n  \"price_per_month\": 19.99\n}"}, "url": {"raw": "{{base_url}}/admin/tiers/1", "host": ["{{base_url}}"], "path": ["admin", "tiers", "1"]}, "description": "Update tier configuration. Requires admin API key."}, "response": []}, {"name": "Get Tier Statistics", "request": {"method": "GET", "header": [{"key": "X-Admin-API-Key", "value": "{{admin_api_key}}"}], "url": {"raw": "{{base_url}}/admin/tiers/2/stats", "host": ["{{base_url}}"], "path": ["admin", "tiers", "2", "stats"]}, "description": "Get usage statistics for a specific tier. Requires admin API key."}, "response": []}, {"name": "Get Admin Info", "request": {"method": "GET", "header": [{"key": "X-Admin-API-Key", "value": "{{admin_api_key}}"}], "url": {"raw": "{{base_url}}/admin/me", "host": ["{{base_url}}"], "path": ["admin", "me"]}, "description": "Get current admin user information."}, "response": []}, {"name": "Get User Credits", "request": {"method": "GET", "header": [{"key": "X-Admin-API-Key", "value": "{{admin_api_key}}"}], "url": {"raw": "{{base_url}}/admin/users/{{user_id}}/credits", "host": ["{{base_url}}"], "path": ["admin", "users", "{{user_id}}", "credits"]}, "description": "Get detailed credit information for a specific user including usage history."}, "response": []}, {"name": "Add Credits to User", "request": {"method": "POST", "header": [{"key": "X-Admin-API-Key", "value": "{{admin_api_key}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"credits_to_add\": 1000,\n  \"reason\": \"Promotional bonus\"\n}"}, "url": {"raw": "{{base_url}}/admin/users/{{user_id}}/credits", "host": ["{{base_url}}"], "path": ["admin", "users", "{{user_id}}", "credits"]}, "description": "Add credits to a user's account with optional reason."}, "response": []}, {"name": "Reset User Monthly Credits", "request": {"method": "POST", "header": [{"key": "X-Admin-API-Key", "value": "{{admin_api_key}}"}], "url": {"raw": "{{base_url}}/admin/users/{{user_id}}/credits/reset", "host": ["{{base_url}}"], "path": ["admin", "users", "{{user_id}}", "credits", "reset"]}, "description": "Reset user's monthly credit usage and restore credits to tier limit."}, "response": []}, {"name": "Credit Analytics", "request": {"method": "GET", "header": [{"key": "X-Admin-API-Key", "value": "{{admin_api_key}}"}], "url": {"raw": "{{base_url}}/admin/analytics/credits?period=month&limit=10", "host": ["{{base_url}}"], "path": ["admin", "analytics", "credits"], "query": [{"key": "period", "value": "month"}, {"key": "limit", "value": "10"}]}, "description": "Get credit usage analytics including top consumers and endpoint usage."}, "response": []}, {"name": "Get All Admin Users", "request": {"method": "GET", "header": [{"key": "X-Admin-API-Key", "value": "{{admin_api_key}}"}], "url": {"raw": "{{base_url}}/admin/admins", "host": ["{{base_url}}"], "path": ["admin", "admins"]}, "description": "Get all admin users. Requires system admin permissions."}, "response": []}, {"name": "Create Admin User", "request": {"method": "POST", "header": [{"key": "X-Admin-API-Key", "value": "{{admin_api_key}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"New Admin\",\n  \"email\": \"<EMAIL>\",\n  \"permissions\": [\"tiers:read\", \"tiers:write\", \"users:read\"]\n}"}, "url": {"raw": "{{base_url}}/admin/admins", "host": ["{{base_url}}"], "path": ["admin", "admins"]}, "description": "Create a new admin user. Requires system admin permissions."}, "response": []}, {"name": "Update Admin User", "request": {"method": "PUT", "header": [{"key": "X-Admin-API-Key", "value": "{{admin_api_key}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Admin Name\",\n  \"permissions\": [\"tiers:read\", \"tiers:write\", \"users:read\", \"users:write\"],\n  \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/admin/admins/{{admin_id}}", "host": ["{{base_url}}"], "path": ["admin", "admins", "{{admin_id}}"]}, "description": "Update an admin user. Requires system admin permissions."}, "response": []}], "description": "Admin API endpoints for tier management, credit administration, and admin user management. Requires admin API key authentication."}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Set default values if not already set", "if (!pm.environment.get('base_url')) {", "    pm.environment.set('base_url', 'http://localhost:3001');", "}", "if (!pm.environment.get('api_key')) {", "    pm.environment.set('api_key', 'demo_api_key_12345');", "}", "if (!pm.environment.get('admin_api_key')) {", "    pm.environment.set('admin_api_key', 'your_admin_api_key_here');", "}"]}}], "variable": [{"key": "base_url", "value": "http://localhost:3001", "type": "string"}, {"key": "api_key", "value": "demo_api_key_12345", "type": "string"}, {"key": "admin_api_key", "value": "your_admin_api_key_here", "type": "string"}, {"key": "admin_id", "value": "admin_user_id_here", "type": "string"}]}
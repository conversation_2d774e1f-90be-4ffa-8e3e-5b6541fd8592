import express from 'express';
import { verifyToken, checkEndpointAccess, checkCredits } from '../middleware/auth.js';
import { consumeCredits, responseTime } from '../middleware/credits.js';
import { createUserRateLimit } from '../middleware/rateLimiter.js';
import { cache } from '../config/redis.js';

const router = express.Router();

// Apply rate limiting to all API routes - temporarily disabled for debugging
// router.use(createUserRateLimit());

// Apply response time tracking
router.use(responseTime);

// Public status endpoint (no auth required)
router.get('/status', async (req, res) => {
    try {
        const status = {
            status: 'healthy',
            timestamp: new Date().toISOString(),
            version: '1.0.0',
            uptime: process.uptime(),
            environment: process.env.NODE_ENV || 'development'
        };
        
        res.json(status);
    } catch (error) {
        res.status(500).json({
            error: 'Status check failed',
            timestamp: new Date().toISOString()
        });
    }
});

// Demo endpoint - accessible to all tiers
router.get('/demo', 
    verifyToken,
    checkEndpointAccess('/api/v1/demo'),
    checkCredits(1),
    consumeCredits(1),
    async (req, res) => {
        try {
            const demoData = {
                message: 'Hello from the API!',
                timestamp: new Date().toISOString(),
                user: {
                    id: req.user.id,
                    email: req.user.email,
                    tier: req.user.tier_name
                },
                random_data: {
                    number: Math.floor(Math.random() * 1000),
                    boolean: Math.random() > 0.5,
                    array: Array.from({ length: 5 }, () => Math.floor(Math.random() * 100))
                }
            };
            
            res.json({
                success: true,
                data: demoData,
                credits_consumed: 1
            });
            
        } catch (error) {
            console.error('Demo endpoint error:', error);
            res.status(500).json({
                error: 'Demo endpoint failed'
            });
        }
    }
);

// Data endpoint - requires basic tier or higher
router.get('/data',
    verifyToken,
    checkEndpointAccess('/api/v1/data'),
    checkCredits(2),
    consumeCredits(2),
    async (req, res) => {
        try {
            const { limit = 10, offset = 0 } = req.query;
            
            // Generate mock data
            const data = Array.from({ length: parseInt(limit) }, (_, index) => ({
                id: parseInt(offset) + index + 1,
                value: Math.random() * 1000,
                category: ['A', 'B', 'C'][Math.floor(Math.random() * 3)],
                timestamp: new Date(Date.now() - Math.random() * 86400000).toISOString(),
                metadata: {
                    source: 'api',
                    quality: Math.random() > 0.8 ? 'high' : 'medium'
                }
            }));
            
            res.json({
                success: true,
                data,
                pagination: {
                    limit: parseInt(limit),
                    offset: parseInt(offset),
                    total: 1000 // Mock total
                },
                credits_consumed: 2
            });
            
        } catch (error) {
            console.error('Data endpoint error:', error);
            res.status(500).json({
                error: 'Data endpoint failed'
            });
        }
    }
);

// Analytics endpoint - requires premium tier or higher
router.get('/analytics',
    verifyToken,
    checkEndpointAccess('/api/v1/analytics'),
    checkCredits(5),
    consumeCredits(5),
    async (req, res) => {
        try {
            const { timeframe = '24h' } = req.query;
            
            // Check cache first
            const cacheKey = `analytics:${timeframe}:${req.user.id}`;
            let analyticsData = await cache.get(cacheKey);
            
            if (!analyticsData) {
                // Generate mock analytics data
                analyticsData = {
                    timeframe,
                    metrics: {
                        total_requests: Math.floor(Math.random() * 10000),
                        avg_response_time: Math.round(Math.random() * 500 * 100) / 100,
                        error_rate: Math.round(Math.random() * 5 * 100) / 100,
                        throughput: Math.round(Math.random() * 1000 * 100) / 100
                    },
                    trends: Array.from({ length: 24 }, (_, hour) => ({
                        hour,
                        requests: Math.floor(Math.random() * 500),
                        errors: Math.floor(Math.random() * 25),
                        avg_response_time: Math.round(Math.random() * 200 * 100) / 100
                    })),
                    top_endpoints: [
                        { endpoint: '/api/v1/data', requests: Math.floor(Math.random() * 1000) },
                        { endpoint: '/api/v1/demo', requests: Math.floor(Math.random() * 800) },
                        { endpoint: '/api/v1/analytics', requests: Math.floor(Math.random() * 300) }
                    ]
                };
                
                // Cache for 5 minutes
                await cache.set(cacheKey, analyticsData, 300);
            }
            
            res.json({
                success: true,
                data: analyticsData,
                credits_consumed: 5,
                cached: !!analyticsData
            });
            
        } catch (error) {
            console.error('Analytics endpoint error:', error);
            res.status(500).json({
                error: 'Analytics endpoint failed'
            });
        }
    }
);

// POST endpoint for data submission
router.post('/submit',
    verifyToken,
    checkEndpointAccess('/api/v1/submit'),
    checkCredits(3),
    consumeCredits(3),
    async (req, res) => {
        try {
            const { data, metadata } = req.body;
            
            if (!data) {
                return res.status(400).json({
                    error: 'Data is required'
                });
            }
            
            // Mock data processing
            const processedData = {
                id: Math.random().toString(36).substring(2, 15),
                original_data: data,
                metadata: metadata || {},
                processed_at: new Date().toISOString(),
                user_id: req.user.id,
                status: 'processed'
            };
            
            // Simulate processing time
            await new Promise(resolve => setTimeout(resolve, Math.random() * 100));
            
            res.json({
                success: true,
                message: 'Data submitted and processed successfully',
                result: processedData,
                credits_consumed: 3
            });
            
        } catch (error) {
            console.error('Submit endpoint error:', error);
            res.status(500).json({
                error: 'Data submission failed'
            });
        }
    }
);

// KOL Feed History endpoint - requires basic tier or higher
router.get('/kol-feed/history',
    verifyToken,
    checkEndpointAccess('/api/v1/kol-feed/history'),
    checkCredits(3),
    consumeCredits(3),
    async (req, res) => {
        try {
            const { limit = 100, offset = 0 } = req.query;
            const maxLimit = 100;
            const requestedLimit = Math.min(parseInt(limit), maxLimit);
            const requestedOffset = Math.max(parseInt(offset), 0);

            // Get KOL feed history from Redis
            const historyData = await cache.lrange('kol_feed_history', requestedOffset, requestedOffset + requestedLimit - 1);
            const totalCount = await cache.llen('kol_feed_history');

            res.json({
                success: true,
                data: historyData,
                pagination: {
                    limit: requestedLimit,
                    offset: requestedOffset,
                    total: totalCount,
                    returned: historyData.length
                },
                credits_consumed: 3,
                message: 'KOL feed history retrieved successfully'
            });

        } catch (error) {
            console.error('KOL feed history endpoint error:', error);
            res.status(500).json({
                error: 'Failed to retrieve KOL feed history'
            });
        }
    }
);

// Batch processing endpoint - enterprise tier only
router.post('/batch',
    verifyToken,
    checkEndpointAccess('/api/v1/batch'),
    checkCredits(10),
    consumeCredits(10),
    async (req, res) => {
        try {
            const { items } = req.body;

            if (!Array.isArray(items) || items.length === 0) {
                return res.status(400).json({
                    error: 'Items array is required and must not be empty'
                });
            }

            if (items.length > 100) {
                return res.status(400).json({
                    error: 'Maximum 100 items allowed per batch'
                });
            }
            
            // Mock batch processing
            const results = items.map((item, index) => ({
                index,
                id: Math.random().toString(36).substring(2, 15),
                input: item,
                output: {
                    processed: true,
                    value: Math.random() * 1000,
                    category: ['success', 'warning', 'info'][Math.floor(Math.random() * 3)]
                },
                processing_time_ms: Math.floor(Math.random() * 50)
            }));
            
            res.json({
                success: true,
                message: `Batch processed ${items.length} items`,
                results,
                summary: {
                    total_items: items.length,
                    processed: results.length,
                    failed: 0,
                    total_processing_time_ms: results.reduce((sum, r) => sum + r.processing_time_ms, 0)
                },
                credits_consumed: 10
            });
            
        } catch (error) {
            console.error('Batch endpoint error:', error);
            res.status(500).json({
                error: 'Batch processing failed'
            });
        }
    }
);

// Search endpoint with caching
router.get('/search',
    verifyToken,
    checkEndpointAccess('/api/v1/search'),
    checkCredits(2),
    consumeCredits(2),
    async (req, res) => {
        try {
            const { q, limit = 10, category } = req.query;
            
            if (!q) {
                return res.status(400).json({
                    error: 'Query parameter "q" is required'
                });
            }
            
            // Check cache first
            const cacheKey = `search:${q}:${limit}:${category || 'all'}`;
            let searchResults = await cache.get(cacheKey);
            
            if (!searchResults) {
                // Mock search results
                searchResults = {
                    query: q,
                    results: Array.from({ length: Math.min(parseInt(limit), 20) }, (_, index) => ({
                        id: index + 1,
                        title: `Result ${index + 1} for "${q}"`,
                        description: `This is a mock search result for query "${q}". Lorem ipsum dolor sit amet.`,
                        category: category || ['tech', 'business', 'science'][Math.floor(Math.random() * 3)],
                        score: Math.round((1 - index * 0.1) * 100) / 100,
                        url: `https://example.com/result/${index + 1}`
                    })),
                    total_results: Math.floor(Math.random() * 1000),
                    search_time_ms: Math.floor(Math.random() * 100)
                };
                
                // Cache for 2 minutes
                await cache.set(cacheKey, searchResults, 120);
            }
            
            res.json({
                success: true,
                data: searchResults,
                credits_consumed: 2,
                cached: !!searchResults
            });
            
        } catch (error) {
            console.error('Search endpoint error:', error);
            res.status(500).json({
                error: 'Search failed'
            });
        }
    }
);

export default router;
